# wxapp 配置
wxapp:
  # 鉴权拦截器
  auth-interceptor:
    # 拦截路径
    path-patterns: /client/wxapp/**,/order/printer/user/**
    # 排除路径
    #    exclude-path-patterns: /client/wxapp/common/**,/client/wxapp/user/login_or_register
    exclude-path-patterns: /client/wxapp/common/**,/client/wxapp/user/login_or_register,/client/wxapp/test/**
  # 微信开发平台
  wechat:
    # 小程序配置
    applets:
      # 小程序appId
      app-id: wxb4c2d62fc737311f
      # 小程序密钥
      app-secret: f37690c219544053db752bbe423d4080
  # 鉴权
  auth:
    # token 过期时间 单位分钟 暂时为1个月
    token-expire-time: 43200
    # token 过期前刷新时间 单位分钟 暂时为3天
    token-refresh-time: 4320
  # 静态资源
  static:
    # 默认
    default:
      # 头像
      avatar: /profile/default/avatar.jpg

# api
api:
  # 日志
  log:
    # 拦截路径
    path-patterns: /client/wxapp/**
    # 排除路径
    exclude-path-patterns: /client/wxapp/test/**

