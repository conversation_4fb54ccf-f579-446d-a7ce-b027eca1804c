package com.ruoyi.web.controller.wxapp.client;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.config.WxPayConfig;
import com.ruoyi.utils.WxPayUtil;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 微信原生支付控制器
 * 直接调用微信支付API，不通过第三方支付网关
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@RestController
@RequestMapping("/client/wxapp/wxpay")
public class CWxNativePayController extends BaseController {

    @Autowired
    private WxPayConfig wxPayConfig;

    @Autowired
    private WxPayUtil wxPayUtil;

    /**
     * 创建微信JSAPI支付订单
     * 
     * @param totalAmount 支付金额(分)
     * @param openId 用户微信OpenID
     * @param outTradeNo 商户订单号
     * @param body 商品描述
     * @return 微信支付参数
     */
    @PostMapping("/jsapi")
    public AjaxResult createJsapiOrder(
            @RequestParam("totalAmount") Integer totalAmount,
            @RequestParam("openId") String openId,
            @RequestParam("outTradeNo") String outTradeNo,
            @RequestParam(value = "body", defaultValue = "商品支付") String body) {
        
        log.info("=== 微信JSAPI支付请求开始 ===");
        log.info("支付金额: {}分, OpenID: {}, 订单号: {}, 商品描述: {}", 
                totalAmount, openId, outTradeNo, body);

        try {
            // 1. 参数校验
            if (totalAmount == null || totalAmount <= 0) {
                return AjaxResult.error("支付金额不能为空或小于等于0");
            }
            if (StringUtils.isEmpty(openId)) {
                return AjaxResult.error("用户OpenID不能为空");
            }
            if (StringUtils.isEmpty(outTradeNo)) {
                return AjaxResult.error("商户订单号不能为空");
            }

            // 2. 获取当前登录用户
            WxappLoginUser currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 3. 构建微信支付请求参数
            Map<String, Object> payRequest = buildJsapiPayRequest(totalAmount, openId, outTradeNo, body);

            // 4. 调用微信支付API
            Map<String, Object> payResult = wxPayUtil.createJsapiOrder(payRequest);

            // 5. 构建前端支付参数
            Map<String, Object> jsapiParams = buildJsapiParams(payResult);

            log.info("=== 微信JSAPI支付请求成功 ===");
            return AjaxResult.success("支付订单创建成功", jsapiParams);

        } catch (Exception e) {
            log.error("微信JSAPI支付请求失败", e);
            return AjaxResult.error("支付订单创建失败: " + e.getMessage());
        }
    }

    /**
     * 创建微信Native支付订单（扫码支付）
     * 
     * @param totalAmount 支付金额(分)
     * @param outTradeNo 商户订单号
     * @param body 商品描述
     * @return 支付二维码URL
     */
    @PostMapping("/native")
    public AjaxResult createNativeOrder(
            @RequestParam("totalAmount") Integer totalAmount,
            @RequestParam("outTradeNo") String outTradeNo,
            @RequestParam(value = "body", defaultValue = "商品支付") String body) {
        
        log.info("=== 微信Native支付请求开始 ===");
        log.info("支付金额: {}分, 订单号: {}, 商品描述: {}", totalAmount, outTradeNo, body);

        try {
            // 1. 参数校验
            if (totalAmount == null || totalAmount <= 0) {
                return AjaxResult.error("支付金额不能为空或小于等于0");
            }
            if (StringUtils.isEmpty(outTradeNo)) {
                return AjaxResult.error("商户订单号不能为空");
            }

            // 2. 构建微信支付请求参数
            Map<String, Object> payRequest = buildNativePayRequest(totalAmount, outTradeNo, body);

            // 3. 调用微信支付API
            Map<String, Object> payResult = wxPayUtil.createNativeOrder(payRequest);

            // 4. 提取二维码URL
            String codeUrl = (String) payResult.get("code_url");
            if (StringUtils.isEmpty(codeUrl)) {
                return AjaxResult.error("获取支付二维码失败");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("codeUrl", codeUrl);
            result.put("outTradeNo", outTradeNo);

            log.info("=== 微信Native支付请求成功 ===");
            return AjaxResult.success("支付二维码生成成功", result);

        } catch (Exception e) {
            log.error("微信Native支付请求失败", e);
            return AjaxResult.error("支付二维码生成失败: " + e.getMessage());
        }
    }

    /**
     * 微信支付回调处理
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 处理结果
     */
    @PostMapping("/notify")
    public String payNotify(HttpServletRequest request, HttpServletResponse response) {
        log.info("=== 微信支付回调开始 ===");

        try {
            // 1. 读取回调数据
            String notifyData = readRequestBody(request);
            log.info("微信支付回调数据: {}", notifyData);

            // 2. 验证签名
            if (!verifyWxPaySignature(request, notifyData)) {
                log.error("微信支付回调签名验证失败");
                return buildNotifyResponse(false, "签名验证失败");
            }

            // 3. 解析回调数据（API v2使用XML格式）
            Map<String, Object> notifyMap = wxPayUtil.xmlToMap(notifyData);
            String returnCode = (String) notifyMap.get("return_code");
            String resultCode = (String) notifyMap.get("result_code");

            if ("SUCCESS".equals(returnCode) && "SUCCESS".equals(resultCode)) {
                // 支付成功处理
                return handlePaymentSuccess(notifyMap);
            } else {
                log.warn("支付回调失败: return_code={}, result_code={}", returnCode, resultCode);
                return buildNotifyResponse(true, "SUCCESS");
            }

        } catch (Exception e) {
            log.error("微信支付回调处理失败", e);
            return buildNotifyResponse(false, "处理失败");
        }
    }

    /**
     * 查询支付订单状态
     * 
     * @param outTradeNo 商户订单号
     * @return 订单状态
     */
    @GetMapping("/query/{outTradeNo}")
    public AjaxResult queryOrder(@PathVariable("outTradeNo") String outTradeNo) {
        log.info("=== 查询支付订单状态: {} ===", outTradeNo);

        try {
            if (StringUtils.isEmpty(outTradeNo)) {
                return AjaxResult.error("订单号不能为空");
            }

            // 调用微信支付查询API
            Map<String, Object> queryResult = wxPayUtil.queryOrder(outTradeNo);

            log.info("=== 订单状态查询成功 ===");
            return AjaxResult.success("查询成功", queryResult);

        } catch (Exception e) {
            log.error("查询支付订单状态失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 构建JSAPI支付请求参数（API v2）
     */
    private Map<String, Object> buildJsapiPayRequest(Integer totalAmount, String openId, String outTradeNo, String body) {
        Map<String, Object> request = new HashMap<>();
        request.put("description", body);
        request.put("out_trade_no", outTradeNo);
        request.put("total_fee", totalAmount);  // API v2使用total_fee，单位为分
        request.put("openid", openId);
        return request;
    }

    /**
     * 构建Native支付请求参数（API v2）
     */
    private Map<String, Object> buildNativePayRequest(Integer totalAmount, String outTradeNo, String body) {
        Map<String, Object> request = new HashMap<>();
        request.put("description", body);
        request.put("out_trade_no", outTradeNo);
        request.put("total_fee", totalAmount);  // API v2使用total_fee，单位为分
        return request;
    }



    /**
     * 构建前端JSAPI支付参数（API v2）
     */
    private Map<String, Object> buildJsapiParams(Map<String, Object> payResult) throws Exception {
        String prepayId = (String) payResult.get("prepay_id");
        if (StringUtils.isEmpty(prepayId)) {
            throw new RuntimeException("获取prepay_id失败");
        }

        Map<String, Object> params = new HashMap<>();
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonceStr = UUID.randomUUID().toString().replace("-", "");
        String packageStr = "prepay_id=" + prepayId;

        params.put("appId", wxPayConfig.getAppId());
        params.put("timeStamp", timeStamp);
        params.put("nonceStr", nonceStr);
        params.put("package", packageStr);
        params.put("signType", "MD5");  // API v2使用MD5签名

        // 生成支付签名
        String paySign = wxPayUtil.generateJsapiPaySign(wxPayConfig.getAppId(), timeStamp, nonceStr, packageStr);
        params.put("paySign", paySign);

        return params;
    }

    /**
     * 读取请求体内容
     */
    private String readRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }

    /**
     * 验证微信支付签名（API v2）
     */
    private boolean verifyWxPaySignature(HttpServletRequest request, String body) {
        try {
            // API v2使用XML格式，需要解析XML获取参数
            Map<String, Object> xmlMap = wxPayUtil.xmlToMap(body);
            Map<String, String> params = new HashMap<>();

            // 转换为String类型的Map
            for (Map.Entry<String, Object> entry : xmlMap.entrySet()) {
                if (entry.getValue() != null) {
                    params.put(entry.getKey(), entry.getValue().toString());
                }
            }

            return wxPayUtil.verifyNotifySignature(params);
        } catch (Exception e) {
            log.error("验证微信支付签名失败", e);
            return false;
        }
    }

    /**
     * 处理支付成功回调（API v2）
     */
    private String handlePaymentSuccess(Map<String, Object> notifyMap) {
        try {
            // 获取订单信息
            String outTradeNo = (String) notifyMap.get("out_trade_no");
            String transactionId = (String) notifyMap.get("transaction_id");
            String totalFee = (String) notifyMap.get("total_fee");
            String openid = (String) notifyMap.get("openid");

            log.info("支付成功 - 商户订单号: {}, 微信订单号: {}, 金额: {}分, OpenID: {}",
                    outTradeNo, transactionId, totalFee, openid);

            // TODO: 根据实际业务需求实现以下逻辑
            // 1. 更新订单状态为已支付
            // 2. 发送支付成功通知
            // 3. 触发后续业务流程

            log.info("支付成功处理完成");
            return buildNotifyResponse(true, "SUCCESS");
        } catch (Exception e) {
            log.error("支付成功处理失败", e);
            return buildNotifyResponse(false, "处理失败");
        }
    }



    /**
     * 构建回调响应
     */
    private String buildNotifyResponse(boolean success, String message) {
        Map<String, String> response = new HashMap<>();
        response.put("code", success ? "SUCCESS" : "FAIL");
        response.put("message", message);
        return JSON.toJSONString(response);
    }
}
