package com.ruoyi.web.controller.wxapp.client;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.config.WxPayConfig;
import com.ruoyi.task.WxPayQueryTask;
import com.ruoyi.utils.WxPayUtil;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.*;
import java.util.List;

/**
 * 微信原生支付控制器
 * 直接调用微信支付API，不通过第三方支付网关
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@RestController
@RequestMapping("/client/wxapp/wxpay")
public class CWxNativePayController extends BaseController {

    @Autowired
    private WxPayConfig wxPayConfig;

    @Autowired
    private WxPayUtil wxPayUtil;



    private String openId ="ol4et7c42VJIsOIR8UVlQ9xH-Tj0";
    /**
     * 创建微信JSAPI支付订单
     * 
     * @param totalAmount 支付金额(分)

     * @param outTradeNo 商户订单号
     * @param body 商品描述
     * @return 微信支付参数
     */
    @PostMapping("/jsapi")
    public AjaxResult createJsapiOrder(
            @RequestParam("totalAmount") Integer totalAmount,
            @RequestParam("outTradeNo") String outTradeNo,
            @RequestParam(value = "body", defaultValue = "商品支付") String body) {


        log.info("=== 微信JSAPI支付请求开始 ===");
        log.info("支付金额: {}分, OpenID: {}, 订单号: {}, 商品描述: {}", 
                totalAmount, openId, outTradeNo, body);

        try {
            // 1. 参数校验
            if (totalAmount == null || totalAmount <= 0) {
                return AjaxResult.error("支付金额不能为空或小于等于0");
            }
            if (StringUtils.isEmpty(openId)) {
                return AjaxResult.error("用户OpenID不能为空");
            }
            if (StringUtils.isEmpty(outTradeNo)) {
                return AjaxResult.error("商户订单号不能为空");
            }

            // 2. 获取当前登录用户
            WxappLoginUser currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 3. 构建微信支付请求参数
            Map<String, Object> payRequest = buildJsapiPayRequest(totalAmount, openId, outTradeNo, body);

            // 4. 调用微信支付API
            Map<String, Object> payResult = wxPayUtil.createJsapiOrder(payRequest);

            // 5. 构建前端支付参数
            Map<String, Object> jsapiParams = buildJsapiParams(payResult);

            // 6. 添加订单相关信息
            jsapiParams.put("outTradeNo", outTradeNo);  // 商户订单号

            // 7. 如果微信返回了prepay_id，也包含进去
            if (payResult.get("prepay_id") != null) {
                jsapiParams.put("prepayId", payResult.get("prepay_id"));
            }

            // 8. 添加微信返回的其他信息
            if (payResult.get("return_code") != null) {
                jsapiParams.put("returnCode", payResult.get("return_code"));
            }
            if (payResult.get("result_code") != null) {
                jsapiParams.put("resultCode", payResult.get("result_code"));
            }

            log.info("=== 微信JSAPI支付请求成功 ===");
            return AjaxResult.success("支付订单创建成功", jsapiParams);

        } catch (Exception e) {
            log.error("微信JSAPI支付请求失败", e);
            return AjaxResult.error("支付订单创建失败: " + e.getMessage());
        }
    }

    /**
     * 创建微信Native支付订单（扫码支付）
     * 
     * @param totalAmount 支付金额(分)
     * @param outTradeNo 商户订单号
     * @param body 商品描述
     * @return 支付二维码URL
     */
    @PostMapping("/native")
    public AjaxResult createNativeOrder(
            @RequestParam("totalAmount") Integer totalAmount,
            @RequestParam("outTradeNo") String outTradeNo,
            @RequestParam(value = "body", defaultValue = "商品支付") String body) {
        
        log.info("=== 微信Native支付请求开始 ===");
        log.info("支付金额: {}分, 订单号: {}, 商品描述: {}", totalAmount, outTradeNo, body);

        try {
            // 1. 参数校验
            if (totalAmount == null || totalAmount <= 0) {
                return AjaxResult.error("支付金额不能为空或小于等于0");
            }
            if (StringUtils.isEmpty(outTradeNo)) {
                return AjaxResult.error("商户订单号不能为空");
            }

            // 2. 构建微信支付请求参数
            Map<String, Object> payRequest = buildNativePayRequest(totalAmount, outTradeNo, body);

            // 3. 调用微信支付API
            Map<String, Object> payResult = wxPayUtil.createNativeOrder(payRequest);

            // 4. 提取二维码URL
            String codeUrl = (String) payResult.get("code_url");
            if (StringUtils.isEmpty(codeUrl)) {
                return AjaxResult.error("获取支付二维码失败");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("codeUrl", codeUrl);
            result.put("outTradeNo", outTradeNo);

            log.info("=== 微信Native支付请求成功 ===");
            return AjaxResult.success("支付二维码生成成功", result);

        } catch (Exception e) {
            log.error("微信Native支付请求失败", e);
            return AjaxResult.error("支付二维码生成失败: " + e.getMessage());
        }
    }

    /**
     * 微信支付回调处理
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 处理结果
     */
    @PostMapping("/notify")
    public String payNotify(HttpServletRequest request, HttpServletResponse response) {
        log.info("=== 微信支付回调开始 ===");

        try {
            // 1. 读取回调数据
            String notifyData = readRequestBody(request);
            log.info("微信支付回调数据: {}", notifyData);

            // 2. 验证签名
            if (!verifyWxPaySignature(request, notifyData)) {
                log.error("微信支付回调签名验证失败");
                return buildNotifyResponse(false, "签名验证失败");
            }

            // 3. 解析回调数据（API v2使用XML格式）
            Map<String, Object> notifyMap = wxPayUtil.xmlToMap(notifyData);
            String returnCode = (String) notifyMap.get("return_code");
            String resultCode = (String) notifyMap.get("result_code");

            if ("SUCCESS".equals(returnCode) && "SUCCESS".equals(resultCode)) {
                // 支付成功处理
                return handlePaymentSuccess(notifyMap);
            } else {
                log.warn("支付回调失败: return_code={}, result_code={}", returnCode, resultCode);
                return buildNotifyResponse(true, "SUCCESS");
            }

        } catch (Exception e) {
            log.error("微信支付回调处理失败", e);
            return buildNotifyResponse(false, "处理失败");
        }
    }

    /**
     * 查询支付订单状态
     *
     * @param outTradeNo 商户订单号
     * @return 订单状态
     */
    @GetMapping("/query/{outTradeNo}")
    public AjaxResult queryOrder(@PathVariable("outTradeNo") String outTradeNo) {
        log.info("=== 查询支付订单状态: {} ===", outTradeNo);

        try {
            if (StringUtils.isEmpty(outTradeNo)) {
                return AjaxResult.error("订单号不能为空");
            }

            // 调用微信支付查询API
            Map<String, Object> queryResult = wxPayUtil.queryOrder(outTradeNo);

            // 解析查询结果
            String returnCode = (String) queryResult.get("return_code");
            String resultCode = (String) queryResult.get("result_code");
            String tradeState = (String) queryResult.get("trade_state");

            Map<String, Object> result = new HashMap<>();
            result.put("outTradeNo", outTradeNo);
            result.put("returnCode", returnCode);
            result.put("resultCode", resultCode);
            result.put("tradeState", tradeState);

            if ("SUCCESS".equals(returnCode) && "SUCCESS".equals(resultCode)) {
                // 查询成功，解析支付状态
                result.put("transactionId", queryResult.get("transaction_id"));
                result.put("totalFee", queryResult.get("total_fee"));
                result.put("openid", queryResult.get("openid"));
                result.put("timeEnd", queryResult.get("time_end"));

                // 判断支付状态
                if ("SUCCESS".equals(tradeState)) {
                    result.put("payStatus", "SUCCESS");
                    result.put("payStatusDesc", "支付成功");

                    // 支付成功时，可以在这里处理业务逻辑
                    handlePaymentSuccessQuery(queryResult);

                } else if ("REFUND".equals(tradeState)) {
                    result.put("payStatus", "REFUND");
                    result.put("payStatusDesc", "转入退款");
                } else if ("NOTPAY".equals(tradeState)) {
                    result.put("payStatus", "NOTPAY");
                    result.put("payStatusDesc", "未支付");
                } else if ("CLOSED".equals(tradeState)) {
                    result.put("payStatus", "CLOSED");
                    result.put("payStatusDesc", "已关闭");
                } else if ("REVOKED".equals(tradeState)) {
                    result.put("payStatus", "REVOKED");
                    result.put("payStatusDesc", "已撤销");
                } else if ("USERPAYING".equals(tradeState)) {
                    result.put("payStatus", "USERPAYING");
                    result.put("payStatusDesc", "用户支付中");
                } else if ("PAYERROR".equals(tradeState)) {
                    result.put("payStatus", "PAYERROR");
                    result.put("payStatusDesc", "支付失败");
                } else {
                    result.put("payStatus", tradeState);
                    result.put("payStatusDesc", "未知状态");
                }
            } else {
                result.put("payStatus", "QUERY_ERROR");
                result.put("payStatusDesc", "查询失败");
                result.put("errorMsg", queryResult.get("err_code_des"));
            }

            log.info("=== 订单状态查询成功: {} ===", result.get("payStatus"));
            return AjaxResult.success("查询成功", result);

        } catch (Exception e) {
            log.error("查询支付订单状态失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量查询订单状态
     *
     * @param outTradeNos 商户订单号列表（逗号分隔）
     * @return 批量查询结果
     */
    @PostMapping("/query/batch")
    public AjaxResult batchQueryOrders(@RequestParam("outTradeNos") String outTradeNos) {
        log.info("=== 批量查询支付订单状态: {} ===", outTradeNos);

        try {
            if (StringUtils.isEmpty(outTradeNos)) {
                return AjaxResult.error("订单号列表不能为空");
            }

            String[] orderArray = outTradeNos.split(",");
            List<Map<String, Object>> results = new ArrayList<>();

            for (String outTradeNo : orderArray) {
                if (StringUtils.isNotEmpty(outTradeNo.trim())) {
                    try {
                        Map<String, Object> queryResult = wxPayUtil.queryOrder(outTradeNo.trim());

                        Map<String, Object> orderResult = new HashMap<>();
                        orderResult.put("outTradeNo", outTradeNo.trim());

                        String returnCode = (String) queryResult.get("return_code");
                        String resultCode = (String) queryResult.get("result_code");
                        String tradeState = (String) queryResult.get("trade_state");

                        if ("SUCCESS".equals(returnCode) && "SUCCESS".equals(resultCode)) {
                            orderResult.put("payStatus", tradeState);
                            orderResult.put("transactionId", queryResult.get("transaction_id"));
                            orderResult.put("totalFee", queryResult.get("total_fee"));

                            if ("SUCCESS".equals(tradeState)) {
                                // 支付成功时处理业务逻辑
                                handlePaymentSuccessQuery(queryResult);
                            }
                        } else {
                            orderResult.put("payStatus", "QUERY_ERROR");
                            orderResult.put("errorMsg", queryResult.get("err_code_des"));
                        }

                        results.add(orderResult);

                    } catch (Exception e) {
                        Map<String, Object> errorResult = new HashMap<>();
                        errorResult.put("outTradeNo", outTradeNo.trim());
                        errorResult.put("payStatus", "EXCEPTION");
                        errorResult.put("errorMsg", e.getMessage());
                        results.add(errorResult);
                    }
                }
            }

            log.info("=== 批量查询完成，共查询{}个订单 ===", results.size());
            return AjaxResult.success("批量查询成功", results);

        } catch (Exception e) {
            log.error("批量查询支付订单状态失败", e);
            return AjaxResult.error("批量查询失败: " + e.getMessage());
        }
    }

    /**
     * 构建JSAPI支付请求参数（API v2）
     */
    private Map<String, Object> buildJsapiPayRequest(Integer totalAmount, String openId, String outTradeNo, String body) {
        Map<String, Object> request = new HashMap<>();
        request.put("description", body);
        request.put("out_trade_no", outTradeNo);
        request.put("total_fee", totalAmount);  // API v2使用total_fee，单位为分
        request.put("openid", openId);
        return request;
    }

    /**
     * 构建Native支付请求参数（API v2）
     */
    private Map<String, Object> buildNativePayRequest(Integer totalAmount, String outTradeNo, String body) {
        Map<String, Object> request = new HashMap<>();
        request.put("description", body);
        request.put("out_trade_no", outTradeNo);
        request.put("total_fee", totalAmount);  // API v2使用total_fee，单位为分
        return request;
    }



    /**
     * 构建前端JSAPI支付参数（API v2）
     */
    private Map<String, Object> buildJsapiParams(Map<String, Object> payResult) throws Exception {
        String prepayId = (String) payResult.get("prepay_id");
        if (StringUtils.isEmpty(prepayId)) {
            throw new RuntimeException("获取prepay_id失败");
        }

        Map<String, Object> params = new HashMap<>();
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonceStr = UUID.randomUUID().toString().replace("-", "");
        String packageStr = "prepay_id=" + prepayId;

        params.put("appId", wxPayConfig.getAppId());
        params.put("timeStamp", timeStamp);
        params.put("nonceStr", nonceStr);
        params.put("package", packageStr);
        params.put("signType", "MD5");  // API v2使用MD5签名

        // 生成支付签名
        String paySign = wxPayUtil.generateJsapiPaySign(wxPayConfig.getAppId(), timeStamp, nonceStr, packageStr);
        params.put("paySign", paySign);

        return params;
    }

    /**
     * 读取请求体内容
     */
    private String readRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }

    /**
     * 验证微信支付签名（API v2）
     */
    private boolean verifyWxPaySignature(HttpServletRequest request, String body) {
        try {
            // API v2使用XML格式，需要解析XML获取参数
            Map<String, Object> xmlMap = wxPayUtil.xmlToMap(body);
            Map<String, String> params = new HashMap<>();

            // 转换为String类型的Map
            for (Map.Entry<String, Object> entry : xmlMap.entrySet()) {
                if (entry.getValue() != null) {
                    params.put(entry.getKey(), entry.getValue().toString());
                }
            }

            return wxPayUtil.verifyNotifySignature(params);
        } catch (Exception e) {
            log.error("验证微信支付签名失败", e);
            return false;
        }
    }

    /**
     * 处理支付成功回调（API v2）
     */
    private String handlePaymentSuccess(Map<String, Object> notifyMap) {
        try {
            // 获取订单信息
            String outTradeNo = (String) notifyMap.get("out_trade_no");
            String transactionId = (String) notifyMap.get("transaction_id");
            String totalFee = (String) notifyMap.get("total_fee");
            String openid = (String) notifyMap.get("openid");

            log.info("支付成功 - 商户订单号: {}, 微信订单号: {}, 金额: {}分, OpenID: {}",
                    outTradeNo, transactionId, totalFee, openid);

            // TODO: 根据实际业务需求实现以下逻辑
            // 1. 更新订单状态为已支付
            // 2. 发送支付成功通知
            // 3. 触发后续业务流程

            log.info("支付成功处理完成");
            return buildNotifyResponse(true, "SUCCESS");
        } catch (Exception e) {
            log.error("支付成功处理失败", e);
            return buildNotifyResponse(false, "处理失败");
        }
    }



    /**
     * 处理查询到的支付成功状态（主动查询时调用）
     */
    private void handlePaymentSuccessQuery(Map<String, Object> queryResult) {
        try {
            String outTradeNo = (String) queryResult.get("out_trade_no");
            String transactionId = (String) queryResult.get("transaction_id");
            String totalFee = (String) queryResult.get("total_fee");
            String openid = (String) queryResult.get("openid");

            log.info("主动查询发现支付成功 - 商户订单号: {}, 微信订单号: {}, 金额: {}分, OpenID: {}",
                    outTradeNo, transactionId, totalFee, openid);

            // TODO: 根据实际业务需求实现以下逻辑
            // 1. 检查订单是否已经处理过（避免重复处理）
            // 2. 更新订单状态为已支付
            // 3. 发送支付成功通知
            // 4. 触发后续业务流程
            // 5. 记录支付成功日志

            log.info("主动查询支付成功处理完成 - 订单号: {}", outTradeNo);

        } catch (Exception e) {
            log.error("处理查询到的支付成功状态失败", e);
        }
    }

    /**
     * 定时查询未支付订单状态
     * 可以通过定时任务调用此方法，主动查询一段时间内的未支付订单
     *
     * @param minutes 查询多少分钟内的订单
     * @return 查询结果
     */
    @PostMapping("/query/recent")
    public AjaxResult queryRecentOrders(@RequestParam(value = "minutes", defaultValue = "30") Integer minutes) {
        log.info("=== 查询最近{}分钟内的订单状态 ===", minutes);

        try {
            // TODO: 根据实际业务需求实现
            // 1. 从数据库查询最近N分钟内创建的未支付订单
            // 2. 批量查询这些订单的支付状态
            // 3. 更新已支付订单的状态

            Map<String, Object> result = new HashMap<>();
            result.put("message", "定时查询功能需要结合具体业务实现");
            result.put("minutes", minutes);

            return AjaxResult.success("查询完成", result);

        } catch (Exception e) {
            log.error("查询最近订单状态失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 构建回调响应
     */
    private String buildNotifyResponse(boolean success, String message) {
        Map<String, String> response = new HashMap<>();
        response.put("code", success ? "SUCCESS" : "FAIL");
        response.put("message", message);
        return JSON.toJSONString(response);
    }
}
