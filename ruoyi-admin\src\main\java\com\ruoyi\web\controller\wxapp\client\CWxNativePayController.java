package com.ruoyi.web.controller.wxapp.client;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.config.WxPayConfig;
import com.ruoyi.utils.WxPayUtil;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 微信原生支付控制器
 * 直接调用微信支付API，不通过第三方支付网关
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@RestController
@RequestMapping("/client/wxapp/wxpay")
public class CWxNativePayController extends BaseController {

    @Autowired
    private WxPayConfig wxPayConfig;

    @Autowired
    private WxPayUtil wxPayUtil;

    /**
     * 创建微信JSAPI支付订单
     * 
     * @param totalAmount 支付金额(分)
     * @param openId 用户微信OpenID
     * @param outTradeNo 商户订单号
     * @param body 商品描述
     * @return 微信支付参数
     */
    @PostMapping("/jsapi")
    public AjaxResult createJsapiOrder(
            @RequestParam("totalAmount") Integer totalAmount,
            @RequestParam("openId") String openId,
            @RequestParam("outTradeNo") String outTradeNo,
            @RequestParam(value = "body", defaultValue = "商品支付") String body) {
        
        log.info("=== 微信JSAPI支付请求开始 ===");
        log.info("支付金额: {}分, OpenID: {}, 订单号: {}, 商品描述: {}", 
                totalAmount, openId, outTradeNo, body);

        try {
            // 1. 参数校验
            if (totalAmount == null || totalAmount <= 0) {
                return AjaxResult.error("支付金额不能为空或小于等于0");
            }
            if (StringUtils.isEmpty(openId)) {
                return AjaxResult.error("用户OpenID不能为空");
            }
            if (StringUtils.isEmpty(outTradeNo)) {
                return AjaxResult.error("商户订单号不能为空");
            }

            // 2. 获取当前登录用户
            WxappLoginUser currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 3. 构建微信支付请求参数
            Map<String, Object> payRequest = buildJsapiPayRequest(totalAmount, openId, outTradeNo, body);

            // 4. 调用微信支付API
            Map<String, Object> payResult = wxPayUtil.createJsapiOrder(payRequest);

            // 5. 构建前端支付参数
            Map<String, Object> jsapiParams = buildJsapiParams(payResult);

            log.info("=== 微信JSAPI支付请求成功 ===");
            return AjaxResult.success("支付订单创建成功", jsapiParams);

        } catch (Exception e) {
            log.error("微信JSAPI支付请求失败", e);
            return AjaxResult.error("支付订单创建失败: " + e.getMessage());
        }
    }

    /**
     * 创建微信Native支付订单（扫码支付）
     * 
     * @param totalAmount 支付金额(分)
     * @param outTradeNo 商户订单号
     * @param body 商品描述
     * @return 支付二维码URL
     */
    @PostMapping("/native")
    public AjaxResult createNativeOrder(
            @RequestParam("totalAmount") Integer totalAmount,
            @RequestParam("outTradeNo") String outTradeNo,
            @RequestParam(value = "body", defaultValue = "商品支付") String body) {
        
        log.info("=== 微信Native支付请求开始 ===");
        log.info("支付金额: {}分, 订单号: {}, 商品描述: {}", totalAmount, outTradeNo, body);

        try {
            // 1. 参数校验
            if (totalAmount == null || totalAmount <= 0) {
                return AjaxResult.error("支付金额不能为空或小于等于0");
            }
            if (StringUtils.isEmpty(outTradeNo)) {
                return AjaxResult.error("商户订单号不能为空");
            }

            // 2. 构建微信支付请求参数
            Map<String, Object> payRequest = buildNativePayRequest(totalAmount, outTradeNo, body);

            // 3. 调用微信支付API
            Map<String, Object> payResult = wxPayUtil.createNativeOrder(payRequest);

            // 4. 提取二维码URL
            String codeUrl = (String) payResult.get("code_url");
            if (StringUtils.isEmpty(codeUrl)) {
                return AjaxResult.error("获取支付二维码失败");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("codeUrl", codeUrl);
            result.put("outTradeNo", outTradeNo);

            log.info("=== 微信Native支付请求成功 ===");
            return AjaxResult.success("支付二维码生成成功", result);

        } catch (Exception e) {
            log.error("微信Native支付请求失败", e);
            return AjaxResult.error("支付二维码生成失败: " + e.getMessage());
        }
    }

    /**
     * 微信支付回调处理
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 处理结果
     */
    @PostMapping("/notify")
    public String payNotify(HttpServletRequest request, HttpServletResponse response) {
        log.info("=== 微信支付回调开始 ===");

        try {
            // 1. 读取回调数据
            String notifyData = readRequestBody(request);
            log.info("微信支付回调数据: {}", notifyData);

            // 2. 验证签名
            if (!verifyWxPaySignature(request, notifyData)) {
                log.error("微信支付回调签名验证失败");
                return buildNotifyResponse(false, "签名验证失败");
            }

            // 3. 解析回调数据
            JSONObject notifyJson = JSON.parseObject(notifyData);
            String eventType = notifyJson.getString("event_type");

            if ("TRANSACTION.SUCCESS".equals(eventType)) {
                // 支付成功处理
                return handlePaymentSuccess(notifyJson);
            } else {
                log.warn("未处理的回调事件类型: {}", eventType);
                return buildNotifyResponse(true, "SUCCESS");
            }

        } catch (Exception e) {
            log.error("微信支付回调处理失败", e);
            return buildNotifyResponse(false, "处理失败");
        }
    }

    /**
     * 查询支付订单状态
     * 
     * @param outTradeNo 商户订单号
     * @return 订单状态
     */
    @GetMapping("/query/{outTradeNo}")
    public AjaxResult queryOrder(@PathVariable("outTradeNo") String outTradeNo) {
        log.info("=== 查询支付订单状态: {} ===", outTradeNo);

        try {
            if (StringUtils.isEmpty(outTradeNo)) {
                return AjaxResult.error("订单号不能为空");
            }

            // 调用微信支付查询API
            Map<String, Object> queryResult = wxPayUtil.queryOrder(outTradeNo);

            log.info("=== 订单状态查询成功 ===");
            return AjaxResult.success("查询成功", queryResult);

        } catch (Exception e) {
            log.error("查询支付订单状态失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 构建JSAPI支付请求参数
     */
    private Map<String, Object> buildJsapiPayRequest(Integer totalAmount, String openId, String outTradeNo, String body) {
        Map<String, Object> request = new HashMap<>();
        request.put("appid", wxPayConfig.getAppId());
        request.put("mchid", wxPayConfig.getMchId());
        request.put("description", body);
        request.put("out_trade_no", outTradeNo);
        request.put("notify_url", wxPayConfig.getNotifyUrl());

        // 订单金额
        Map<String, Object> amount = new HashMap<>();
        amount.put("total", totalAmount);
        amount.put("currency", "CNY");
        request.put("amount", amount);

        // 支付者信息
        Map<String, Object> payer = new HashMap<>();
        payer.put("openid", openId);
        request.put("payer", payer);

        return request;
    }

    /**
     * 构建Native支付请求参数
     */
    private Map<String, Object> buildNativePayRequest(Integer totalAmount, String outTradeNo, String body) {
        Map<String, Object> request = new HashMap<>();
        request.put("appid", wxPayConfig.getAppId());
        request.put("mchid", wxPayConfig.getMchId());
        request.put("description", body);
        request.put("out_trade_no", outTradeNo);
        request.put("notify_url", wxPayConfig.getNotifyUrl());

        // 订单金额
        Map<String, Object> amount = new HashMap<>();
        amount.put("total", totalAmount);
        amount.put("currency", "CNY");
        request.put("amount", amount);

        return request;
    }



    /**
     * 构建前端JSAPI支付参数
     */
    private Map<String, Object> buildJsapiParams(Map<String, Object> payResult) throws Exception {
        String prepayId = (String) payResult.get("prepay_id");
        if (StringUtils.isEmpty(prepayId)) {
            throw new RuntimeException("获取prepay_id失败");
        }

        Map<String, Object> params = new HashMap<>();
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonceStr = UUID.randomUUID().toString().replace("-", "");
        String packageStr = "prepay_id=" + prepayId;

        params.put("appId", wxPayConfig.getAppId());
        params.put("timeStamp", timeStamp);
        params.put("nonceStr", nonceStr);
        params.put("package", packageStr);
        params.put("signType", "RSA");

        // 生成支付签名
        String paySign = wxPayUtil.generateJsapiPaySign(wxPayConfig.getAppId(), timeStamp, nonceStr, packageStr);
        params.put("paySign", paySign);

        return params;
    }

    /**
     * 读取请求体内容
     */
    private String readRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }

    /**
     * 验证微信支付签名
     */
    private boolean verifyWxPaySignature(HttpServletRequest request, String body) {
        try {
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");
            String signature = request.getHeader("Wechatpay-Signature");

            if (StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(nonce) || StringUtils.isEmpty(signature)) {
                log.error("微信支付回调缺少必要的签名头信息");
                return false;
            }

            return wxPayUtil.verifyNotifySignature(timestamp, nonce, body, signature);
        } catch (Exception e) {
            log.error("验证微信支付签名失败", e);
            return false;
        }
    }

    /**
     * 处理支付成功回调
     */
    private String handlePaymentSuccess(JSONObject notifyJson) {
        try {
            // 解析回调数据
            JSONObject resource = notifyJson.getJSONObject("resource");
            if (resource == null) {
                log.error("回调数据中缺少resource字段");
                return buildNotifyResponse(false, "数据格式错误");
            }

            // 解密回调数据（如果需要）
            String ciphertext = resource.getString("ciphertext");
            String associatedData = resource.getString("associated_data");
            String nonce = resource.getString("nonce");

            // 这里可以解密获取真实的支付数据
            // String decryptedData = wxPayUtil.decryptNotifyData(associatedData, nonce, ciphertext);

            // 获取订单信息
            String outTradeNo = resource.getString("out_trade_no");
            String transactionId = resource.getString("transaction_id");

            log.info("支付成功 - 商户订单号: {}, 微信订单号: {}", outTradeNo, transactionId);

            // TODO: 根据实际业务需求实现以下逻辑
            // 1. 更新订单状态为已支付
            // 2. 发送支付成功通知
            // 3. 触发后续业务流程

            log.info("支付成功处理完成");
            return buildNotifyResponse(true, "SUCCESS");
        } catch (Exception e) {
            log.error("支付成功处理失败", e);
            return buildNotifyResponse(false, "处理失败");
        }
    }



    /**
     * 构建回调响应
     */
    private String buildNotifyResponse(boolean success, String message) {
        Map<String, String> response = new HashMap<>();
        response.put("code", success ? "SUCCESS" : "FAIL");
        response.put("message", message);
        return JSON.toJSONString(response);
    }
}
