package com.ruoyi.web.controller.wxapp.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.config.MyConfig;
import com.ruoyi.device.domain.DevicePrinter;
import com.ruoyi.device.domain.DevicePrinter;
import com.ruoyi.device.service.IDevicePrinterService;
import com.ruoyi.enums.ChannelTypeEnum;
import com.ruoyi.enums.FunctionEnum;
import com.ruoyi.fee.domain.Fee;
import com.ruoyi.fee.service.IFeeService;
import com.ruoyi.order.domain.OrderPrinter;
import com.ruoyi.order.domain.OrderPrinterTask;
import com.ruoyi.order.domain.OrderCollect;
import com.ruoyi.order.service.IOrderPrinterService;
import com.ruoyi.order.service.IOrderCollectService;
import com.ruoyi.po.JsPay;
import com.ruoyi.po.SplitBunch;
import com.ruoyi.po.SplitInfo;
import com.ruoyi.socket.WebSocketServer;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.utils.HttpsMain;
import com.ruoyi.utils.RsaUtil;
import com.ruoyi.common.utils.StringUtils;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.service.WxAppAuthService;
import com.yunchuang.wxapp.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

import static com.ruoyi.common.constant.OrderConstants.*;

/**
 * 打印机支付控制器
 * 模拟HLPay.java的支付功能，但针对打印机订单
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
@Controller
@RequestMapping("/client/wxapp/printer/pay")
public class CPrinterPayController {

    // 测试开关：是否启用分账功能（false=简化支付测试，true=完整分账）
    private static final boolean ENABLE_SPLIT_ACCOUNT = false;
    private static final String YC_MCHID = "**********";
    private static final String PDL_YC_MCHID = "**********";

    @Autowired
    private IOrderPrinterService orderPrinterService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IOrderCollectService orderCollectService;
    @Autowired
    private IDevicePrinterService devicePrinterService;
    @Autowired
    private IFeeService feeService;
    @Autowired
    private WxAppAuthService wxAppAuthService;

    /**
     * 获取支付页面接口
     * 功能：根据订单信息和用户类型，返回相应的支付页面
     *
     * @param request HTTP请求对象
     * @return 支付页面视图或重定向URL
     * @throws UnsupportedEncodingException URL编码异常
     */
    @GetMapping("/getOpenId")
    public Object getOpenId(HttpServletRequest request) throws UnsupportedEncodingException {
        log.info("=== 打印机支付页面请求开始 ===");

        // 1. 获取请求参数
        String attach = request.getParameter("attach");        // 订单ID
        String userInfo = request.getParameter("userInfo");    // 用户信息JSON字符串（可选）

        log.info("接收参数 - 订单ID: {}, 用户信息: {}", attach, userInfo);

        // 2. 优先从ThreadLocal获取openid，其次从请求参数解析
        String openId = getCurrentUserOpenid(request);

        // 如果ThreadLocal中没有获取到，尝试从请求参数解析
        if (StringUtils.isEmpty(openId) && StringUtils.isNotEmpty(userInfo)) {
            JSONObject jsonObject = JSONObject.parseObject(userInfo);
            if (jsonObject != null) {
                openId = jsonObject.getString("openid");
                log.info("从请求参数解析得到openid: {}", openId);
            }
        } else if (StringUtils.isNotEmpty(openId)) {
            log.info("从ThreadLocal获取到openid: {}", openId);
        }

        // 3. 参数验证
        if (attach == null) {
            log.error("订单ID不能为空");
            return null;
        }

        if (StringUtils.isEmpty(openId)) {
            log.error("无法获取用户openid - ThreadLocal和请求参数中都未找到");
            return null;
        }

        // 4. 查询订单信息
        OrderPrinter orderPrinter = orderPrinterService.selectOrderPrinterByOrderId(attach);
        if (orderPrinter == null) {
            log.error("订单不存在: {}", attach);
            return null;
        }
        orderPrinter.setMchid("1366993712427");
        orderPrinter.setUserId(Long.valueOf( 130));
        log.info("订单信息 - ID: {}, 金额: {}, 商户号: {}, 用户ID: {}",
                orderPrinter.getOrderId(), orderPrinter.getTotalAmount(),
                orderPrinter.getMchid(), orderPrinter.getUserId());

        // 5. 检查订单基本信息
        if (orderPrinter.getTotalAmount() == null || orderPrinter.getTotalAmount() <= 0) {
            log.error("订单金额异常: orderId={}, amount={}", orderPrinter.getOrderId(), orderPrinter.getTotalAmount());
            return "error: 订单金额异常，请重新创建订单";
        }

        // 5. 可选：通知设备扫码成功
        // WebSocketServer.sendInfo("smcg", orderPrinter.getDeviceId()); // 主推信息->扫码成功

        // 6. 根据用户所属部门判断支付方式
        // 6.1 检查订单是否有关联用户
        if (orderPrinter.getUserId() == null) {
            log.error("订单未关联用户: orderId={}", orderPrinter.getOrderId());
            return "error: 订单数据异常，未关联用户信息";
        }

        // 6.2 查询用户信息
        SysUser sysUser = userService.selectUserById(orderPrinter.getUserId());
        if (sysUser == null) {
            log.error("用户不存在: userId={}, orderId={}", orderPrinter.getUserId(), orderPrinter.getOrderId());
            return "error: 用户信息不存在，请联系管理员";
        }

        // 6.3 检查用户部门信息
        if (sysUser.getDept() == null) {
            log.error("用户部门信息缺失: userId={}, orderId={}", sysUser.getUserId(), orderPrinter.getOrderId());
            return "error: 用户部门信息缺失，请联系管理员";
        }

        log.info("用户信息 - ID: {}, 部门ID: {}, 部门层级: {}",
                sysUser.getUserId(), sysUser.getDeptId(), sysUser.getDept().getAncestors());

        // 6.4 处理商户号信息
        String mchid = orderPrinter.getMchid();
        if (StringUtils.isEmpty(mchid)) {
            // 如果订单没有商户号，尝试从用户信息获取
            if (StringUtils.isNotEmpty(sysUser.getMerchantId())) {
                mchid = sysUser.getMerchantId();
                log.info("从用户信息获取商户号: userId={}, mchid={}", sysUser.getUserId(), mchid);
            } else {
                log.error("商户号信息缺失: userId={}, orderId={}", sysUser.getUserId(), orderPrinter.getOrderId());
                return "error: 商户号信息缺失，请联系管理员配置";
            }
        }

        // 7. 根据用户部门判断支付方式
        if (sysUser.getDeptId() != 201 && !sysUser.getDept().getAncestors().contains("201")) {
            log.info("云创用户，重定向到外部支付页面");

            // 构建外部支付页面URL
            String url = "http://ypjh.cameraon.store/pay";
            url += "?totalAmount=" + (orderPrinter.getTotalAmount() != null ? orderPrinter.getTotalAmount() : 0);
            url += "&openId=" + openId;
            url += "&orderId=" + attach;
            url += "&photoTypeName=" + URLEncoder.encode("打印服务","UTF-8") ;
            url += "&mchid=" + orderPrinter.getMchid();

            log.info("重定向URL: {}", url);
            return "redirect:" + url;
        }

        // 6.2 潘朵拉用户：返回内部支付页面
        log.info("潘朵拉用户，返回内部支付页面");

        ModelAndView modelAndView = null;

        // 根据商户号选择支付页面模板
        if (orderPrinter.getMchid().equals("1398949712874")) {
            // 乐高的商户号有定制的支付页面
            log.info("使用乐高定制支付页面");
            modelAndView = new ModelAndView("jsPayLego");
        } else {
            // 使用默认支付页面
            log.info("使用默认支付页面");
            modelAndView = new ModelAndView("jsPay");
        }

        // 7. 向支付页面传递必要参数
        Long totalAmount = orderPrinter.getTotalAmount() != null ? orderPrinter.getTotalAmount() : 0;
        modelAndView.addObject("totalAmount", totalAmount);
        modelAndView.addObject("openId", openId);
        modelAndView.addObject("orderId", attach);
        modelAndView.addObject("mchid", orderPrinter.getMchid());

        log.info("支付页面参数 - 金额: {}, openId: {}, 订单ID: {}, 商户号: {}",
                totalAmount, openId, attach, orderPrinter.getMchid());
        log.info("=== 打印机支付页面请求完成 ===");

        return modelAndView;
    }

   

    /**
     * 发起JS支付
     *
     * @param totalAmount
     * @return
     * @throws IOException
     */
    @ResponseBody
    @PostMapping("/jsPay")
    public String doJsPay(int totalAmount, String openId, String outTradeNo, String mchid) throws Exception {

        OrderPrinter orderPrinter = orderPrinterService.selectOrderPrinterByOrderId(outTradeNo);
        if (orderPrinter == null) {
            return "订单号错误";
        }

        SysUser user = userService.selectUserById(orderPrinter.getUserId());
        if (user == null) {
            return "用户绑定错误";
        }

        boolean isPDL = false;
        String ancestors = user.getDept().getAncestors();
        Long deptId = user.getDept().getDeptId();
        if (ancestors.contains("201") || deptId == 201) {
            isPDL = true;
        }

        if (orderPrinter.getOrderStatus() != 0) {
            return "订单状态错误" + orderPrinter.getOrderStatus();
        }

        JsPay jsPay = new JsPay();
        jsPay.setOutTradeNo(outTradeNo);
        jsPay.setWxAppId(MyConfig.WX_APPID);
        jsPay.setBody("打印付款");
        jsPay.setHlMerchantId(mchid);
        /**去掉小数点后两位*/
        jsPay.setTotalAmount(String.valueOf(totalAmount));

        // 只支持微信支付
        if (openId == null || openId.equals("") || openId.equals("null")) {
            return "缺少微信openId";
        }
        jsPay.setOpenId(openId);
        orderPrinter.setOpenid(openId);
        jsPay.setChannelType(ChannelTypeEnum.WX.getCode());

        jsPay.setNotifyUrl(MyConfig.Notice_URL);   //回调地址
        jsPay.setSucUrl("https://fotobox.cameraon.store/digitalPhotoTongNiu?orderId=" + outTradeNo);  //支付成功跳转页面 todo 需动态设置

        DevicePrinter device = devicePrinterService.selectDevicePrinterByDeviceId(orderPrinter.getDeviceId());

        if (device == null)
            return "设备绑定异常";

        String subAccount = device.getSubAccount();
        // 打印机暂时不收取特殊费用，可以根据需要扩展
        Fee printFee = null; // feeService.query().eq("print_type", "PRINTER").one();

        // 检查是否启用分账功能
        if (!ENABLE_SPLIT_ACCOUNT) {
            // 简化模式：不分账，全部金额给商户
            log.info("使用简化支付模式，不进行分账处理");
            orderPrinter.setMoneyReceived((long) totalAmount);
            orderPrinter.setInterfaceFee(0L);
            orderPrinter.setCommission((long) Math.round(totalAmount * 0.006f)); // 基础手续费
            orderPrinter.setAccount(orderPrinter.getMoneyReceived() - orderPrinter.getCommission());
            orderPrinterService.updateOrderPrinter(orderPrinter);

            // 不设置分账信息，使用默认支付
            // jsPay.setSplitBunch() 不调用，汇联会使用默认的单商户支付

        } else if (subAccount != null && !subAccount.equals("")) {// 完整分账模式

            String[] FZitem = subAccount.split(",");
            int length = FZitem.length;

            //新分账逻辑//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            String[] peopleMchids = new String[length];
            int[] peopleRates = new int[length];

            for (int i = 0; i < length; i++) {
                String[] people = FZitem[i].split(":");
                peopleMchids[i] = people[0];                       //商户号列表
                peopleRates[i] = Integer.parseInt(people[1]);      //分账比例列表
            }
            int[] moneys = splitInt(totalAmount, peopleRates);     //分钱列表

            Map<String, Integer> peopleMoneyMap = new HashMap<>();
            for (int i = 0; i < length; i++) {
                peopleMoneyMap.put(peopleMchids[i], moneys[i]);      // key：商户    value：分账金额
            }

            //如果特定类型收取手续费
            int fee = 0;
            if (printFee != null && (printFee.getExcludeDevice() == null || !printFee.getExcludeDevice().contains(orderPrinter.getDeviceId()))) {
                Integer chargeType = printFee.getChargeType();

                if (chargeType == 1) { //金额
                    fee = printFee.getFeeAmount();
                } else {               //比例
                    float feeRate = printFee.getFeeRate();
                    fee = (int) (totalAmount * feeRate);
                }
            }
            int i = 0;
            if (fee > 0) {
                if (peopleMoneyMap.containsKey(YC_MCHID)) {
                    peopleMoneyMap.put(YC_MCHID, peopleMoneyMap.get(YC_MCHID) + fee);
                } else if (peopleMoneyMap.containsKey(PDL_YC_MCHID)) {
                    peopleMoneyMap.put(PDL_YC_MCHID, peopleMoneyMap.get(PDL_YC_MCHID) + fee);
                } else {
                    peopleMoneyMap.put(!isPDL ? YC_MCHID : PDL_YC_MCHID, fee);
                }
                peopleMoneyMap.put(mchid, peopleMoneyMap.get(mchid) - fee);
                createInterfaceFeeOrder(orderPrinter, !isPDL ? YC_MCHID : PDL_YC_MCHID, fee, i++);
            }

            List<SplitInfo> acctInfos = new ArrayList<>();

            for (Map.Entry<String, Integer> entry : peopleMoneyMap.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                acctInfos.add(new SplitInfo(String.valueOf(entry.getValue()), entry.getKey()));

                if (entry.getKey().equals(mchid)) {              //订单主人
                    orderPrinter.setMoneyReceived(Long.valueOf(entry.getValue()));     //实收
                    orderPrinter.setInterfaceFee((long) fee);//接口费
                    orderPrinter.setCommission((long) Math.round(totalAmount * 0.006f));//手续费
                    orderPrinter.setAccount(orderPrinter.getMoneyReceived() - orderPrinter.getInterfaceFee() - orderPrinter.getCommission());  //到账
                    orderPrinterService.updateOrderPrinter(orderPrinter);
                } else {
                    createFZOrder(orderPrinter, entry.getKey(), entry.getValue() - fee, i++);
                }
            }

            //新分账逻辑///////////////////////////////////////////////////////////////

            SplitBunch splitBunch = new SplitBunch(String.valueOf(length), mchid, acctInfos);
            String jsonString = JSON.toJSONString(splitBunch);
            String encode = Base64.getEncoder().encodeToString(jsonString.getBytes());
            jsPay.setSplitBunch(encode);
        } else { //不分账
            if (printFee != null && (printFee.getExcludeDevice() == null || !printFee.getExcludeDevice().equals("all") || !printFee.getExcludeDevice().contains(orderPrinter.getDeviceId()))) {  //特定类型收取手续费
                Integer chargeType = printFee.getChargeType();
                int fee;
                if (chargeType == 1) { //金额
                    fee = printFee.getFeeAmount();
                } else {                    //比例
                    float feeRate = printFee.getFeeRate();
                    fee = (int) (totalAmount * feeRate);
                }
                List<SplitInfo> acctInfos = new ArrayList<>();
                acctInfos.add(new SplitInfo(String.valueOf(totalAmount - fee), mchid));
                acctInfos.add(new SplitInfo(String.valueOf(fee), !isPDL ? YC_MCHID : PDL_YC_MCHID));
                createInterfaceFeeOrder(orderPrinter, !isPDL ? YC_MCHID : PDL_YC_MCHID, fee, 0);

                orderPrinter.setMoneyReceived((long) (totalAmount));//实收
                orderPrinter.setInterfaceFee((long) fee);//接口费
                orderPrinter.setCommission((long) Math.round(totalAmount * 0.006f));//手续费
                orderPrinter.setAccount(orderPrinter.getMoneyReceived() - orderPrinter.getInterfaceFee() - orderPrinter.getCommission()); //到账
                orderPrinterService.updateOrderPrinter(orderPrinter);

                SplitBunch splitBunch = new SplitBunch("2", mchid, acctInfos);
                String jsonString = JSON.toJSONString(splitBunch);
                String encode = Base64.getEncoder().encodeToString(jsonString.getBytes());
                jsPay.setSplitBunch(encode);
            } else {
                orderPrinter.setMoneyReceived((long) totalAmount);
                orderPrinter.setCommission((long) Math.round(totalAmount * 0.006f));//手续费
                orderPrinter.setAccount(orderPrinter.getMoneyReceived() - orderPrinter.getCommission());
                orderPrinterService.updateOrderPrinter(orderPrinter);
            }
         // 结束完整分账模式的大括号
        } // 结束整个分账逻辑的大括号

        String myAgencyNo = MyConfig.AgencyNo_yc;
        String myPrivateKey = MyConfig.PrivateKey_yc;
        if (isPDL) {
            myAgencyNo = MyConfig.AgencyNo;
            myPrivateKey = MyConfig.PrivateKey;
        }

        String param = HttpsMain.format(jsPay, FunctionEnum.JS_PAY, myAgencyNo, myPrivateKey);
        log.info("请求报文{}", param);

        String response = HttpsMain.httpReq(MyConfig.PayUrl, param);
        log.info("响应报文{}", response);
        //验签
        if (!RsaUtil.verifyResponseSign(response)) {
            throw new Exception("验签失败");
        }

        JSONObject jsonObject = JSON.parseObject(response);

        String status = jsonObject.getString("status");
        String code = jsonObject.getString("code");
        if ("S".equals(status) && "0000".equals(code)) {
            //JSON.parseObject(response).getString("data"),取得返回结果的其中一个值 转化为Map
            Map<String, String> res = JSON.parseObject(jsonObject.getString("data"), new TypeReference<Map<String, String>>() {
            });
            Map<String, String> map = new HashMap<>();
            map.put("channelType", ChannelTypeEnum.WX.getCode());
            map.put("data", res.get("payInfo")); // 微信支付返回payInfo
            log.info("返回出去的数据{}", map);

            return JSON.toJSONString(map);
        } else {
            String msg = jsonObject.getString("msg");
            throw new Exception(msg);
        }
    }

    /**
     * 分割整数按比例分配
     */
    private int[] splitInt(int total, int[] ratios) {
        int length = ratios.length;
        int[] results = new int[length];

        // 计算总比例
        int sumOfRatios = Arrays.stream(ratios).sum();

        // 初步分配
        int allocatedSum = 0;
        for (int i = 0; i < length; i++) {
            results[i] = total * ratios[i] / sumOfRatios;
            allocatedSum += results[i];
        }

        // 分配余数
        int remainder = total - allocatedSum;
        for (int i = 0; remainder > 0; i = (i + 1) % length) {
            results[i]++;
            remainder--;
        }
        return results;
    }

    /**
     * 创建分账订单
     */
    private void createFZOrder(OrderPrinter orderPrinter, String mchId, int moneyReceived, int i) {
        List<SysUser> users = userService.query().eq("merchant_id", mchId).list();

        OrderPrinter fzOrder = new OrderPrinter();
        BeanUtils.copyProperties(orderPrinter, fzOrder);
        fzOrder.setOrderId(i + "_" + orderPrinter.getOrderId());
        fzOrder.setMchid(mchId);
        if (users.size() > 0)
            fzOrder.setUserId(users.get(0).getUserId());

        fzOrder.setMoneyReceived((long) moneyReceived);
        fzOrder.setCommission(0L);
        fzOrder.setInterfaceFee(0L);
        fzOrder.setAccount((long) moneyReceived);

        fzOrder.setDeviceName(fzOrder.getDeviceName() + "_分账订单");

        if (orderPrinterService.selectOrderPrinterByOrderId(fzOrder.getOrderId()) == null)
            orderPrinterService.insertOrderPrinter(fzOrder);
    }

    /**
     * 创建接口费订单
     */
    private void createInterfaceFeeOrder(OrderPrinter orderPrinter, String mchId, int interfaceFee, int i) {
        List<SysUser> users = userService.query().eq("merchant_id", mchId).list();

        OrderPrinter InterfaceFeeOrder = new OrderPrinter();
        BeanUtils.copyProperties(orderPrinter, InterfaceFeeOrder);
        InterfaceFeeOrder.setOrderId(i + "_" + orderPrinter.getOrderId());
        InterfaceFeeOrder.setMchid(mchId);
        if (users.size() > 0)
            InterfaceFeeOrder.setUserId(users.get(0).getUserId());

        InterfaceFeeOrder.setMoneyReceived(0L);
        InterfaceFeeOrder.setInterfaceFee((long) interfaceFee);
        InterfaceFeeOrder.setCommission(0L);
        InterfaceFeeOrder.setAccount((long) interfaceFee);

        InterfaceFeeOrder.setDeviceName(InterfaceFeeOrder.getDeviceName() + "_打印-手续费");

        if (orderPrinterService.selectOrderPrinterByOrderId(InterfaceFeeOrder.getOrderId()) == null)
            orderPrinterService.insertOrderPrinter(InterfaceFeeOrder);
    }

    /**
     * 支付回调通知
     */
    @ResponseBody
    @PostMapping("/notice")
    public String notice(HttpServletRequest request) throws Exception {
        BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
        String tempLine = "";
        StringBuffer resultBuffer = new StringBuffer();
        while ((tempLine = reader.readLine()) != null) {
            resultBuffer.append(tempLine).append(System.getProperty("line.separator"));
        }
        String response = resultBuffer.toString();
        com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSONObject.parseObject(response);
        com.alibaba.fastjson2.JSONObject data = jsonObject.getJSONObject("data");
        System.out.println("回调报文：" + data);
        String orderId = data.getString("outTradeNo");
        String transactionId = data.getString("payChannelOrderNo");
        Date payTime = data.getDate("gmtPayment");

        OrderPrinter orderPrinter = orderPrinterService.selectOrderPrinterByOrderId(orderId);

        // 验证订单状态，避免重复处理
        if (orderPrinter.getOrderStatus() != 0) {
            log.warn("订单状态异常，可能重复回调: orderId={}, currentStatus={}", orderId, orderPrinter.getOrderStatus());
            return "SUCCESS"; // 已处理过，直接返回成功
        }

        // 更新订单支付信息
        orderPrinter.setPayTime(payTime);
        orderPrinter.setTransactionId(transactionId);
        orderPrinter.setOrderStatus(1); // 已支付，等待打印
        orderPrinter.setPayWay(1); // 微信支付
        orderPrinterService.updateOrderPrinter(orderPrinter);

        // 更新所有关联的打印任务状态
        updatePrintTasksStatus(orderId);

        // 通知设备开始打印
        // notifyDeviceToPrint(orderPrinter);

        log.info("打印订单支付成功处理完成: orderId={}, deviceId={}, totalAmount={}",
                orderId, orderPrinter.getDeviceId(), orderPrinter.getTotalAmount());

        // 处理分账订单（仅在启用分账时）
        List<OrderPrinter> _orders = new ArrayList<>();
        if (ENABLE_SPLIT_ACCOUNT) {
            OrderPrinter searchOrder = new OrderPrinter();
            searchOrder.setOrderId("_" + orderPrinter.getOrderId());
            _orders = orderPrinterService.selectOrderPrinterList(searchOrder);
            if (_orders.size() > 0) {//分账已支付
                for (OrderPrinter _order : _orders) {
                    _order.setOrderStatus(1);
                    _order.setTransactionId(transactionId);
                    _order.setPayTime(payTime);
                    orderPrinterService.updateOrderPrinter(_order);
                }
                log.info("分账订单状态更新完成: 主订单={}, 分账订单数量={}", orderId, _orders.size());
            }
        } else {
            log.info("简化支付模式，跳过分账订单处理: orderId={}", orderId);
        }

        try {
            //汇总（主订单）
            OrderCollect collect = orderCollectService.query()
                .eq("merchant_id", orderPrinter.getMchid())
                .eq("time", DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderPrinter.getCreateTime()))
                .one();

            if (collect == null) {
                collect = new OrderCollect();
                collect.setMerchantId(orderPrinter.getMchid());
                collect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderPrinter.getCreateTime()));
            }

            // 打印机订单统计到其他收入
            setCollectForPrinter(orderPrinter, collect);

            //如果存在分账订单
            if (_orders.size() > 0) {
                //（汇总 分账人）
                for (OrderPrinter _order : _orders) {
                    OrderCollect _collect = orderCollectService.query()
                        .eq("merchant_id", _order.getMchid())
                        .eq("time", DateUtils.parseDateToStr(DateUtils.YYYY_MM, _order.getCreateTime()))
                        .one();

                    if (_collect == null) {
                        _collect = new OrderCollect();
                        _collect.setMerchantId(_order.getMchid());
                        _collect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, _order.getCreateTime()));
                    }
                    setCollectForPrinter(_order, _collect);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        //更新设备表中的设备总收入
        DevicePrinter devicePrinter = devicePrinterService.selectDevicePrinterByDeviceId(orderPrinter.getDeviceId());
        if (devicePrinter != null) {
            Long currentPrice = devicePrinter.getCountPrice() != null ? devicePrinter.getCountPrice() : 0L;
            Long orderAmount = orderPrinter.getTotalAmount() != null ? orderPrinter.getTotalAmount() : 0L;
            devicePrinter.setCountPrice(currentPrice + orderAmount);
            devicePrinterService.updateDevicePrinter(devicePrinter);
        }

        if (!RsaUtil.verifyResponseSign(response)) {
            throw new Exception("验签失败");
        }
        return "SUCCESS";
    }

    /**
     * 设置打印机订单收入统计
     */
    private void setCollectForPrinter(OrderPrinter order, OrderCollect collect) {
        // 打印机订单统计到其他收入
        Long account = order.getAccount() != null ? order.getAccount() : 0L;
        collect.setOtherIncome(collect.getOtherIncome() + account);
        collect.setOtherOrderCount(collect.getOtherOrderCount() + 1);
        collect.setCount(collect.getCount() + account);

        if (collect.getId() != null) {
            orderCollectService.updateById(collect);
        } else {
            orderCollectService.save(collect);
        }
    }

    /**
     * 更新打印任务状态
     * 支付成功后，将所有关联的打印任务状态更新为"等待打印"
     */
    private void updatePrintTasksStatus(String orderId) {
        try {
            List<OrderPrinterTask> tasks = orderPrinterService.getOrderTasks(orderId);
            if (tasks != null && !tasks.isEmpty()) {
                for (OrderPrinterTask task : tasks) {
                    if (task.getPrintStatus() == 0) { // 只更新待打印的任务
                        task.setPrintStatus(0); // 0-待打印（确保状态正确）
                        orderPrinterService.updateTask(task);
                    }
                }
                log.info("更新打印任务状态成功: orderId={}, taskCount={}", orderId, tasks.size());
            }
        } catch (Exception e) {
            log.error("更新打印任务状态失败: orderId={}, error={}", orderId, e.getMessage(), e);
        }
    }

    /**
     * 通知设备开始打印
     * 支付成功后通知打印机设备开始执行打印任务
     */
    private void notifyDeviceToPrint(OrderPrinter orderPrinter) {
        try {
            String deviceId = orderPrinter.getDeviceId();
            if (StringUtils.isNotEmpty(deviceId)) {
                // 发送支付成功消息，设备收到后开始打印
                int result = WebSocketServer.sendInfo("zfcg", deviceId);
                if (result > 0) {
                    log.info("设备通知发送成功: deviceId={}, orderId={}", deviceId, orderPrinter.getOrderId());
                } else {
                    log.warn("设备通知发送失败，设备可能离线: deviceId={}, orderId={}", deviceId, orderPrinter.getOrderId());
                    // 可以考虑记录到队列，等设备上线后重新发送
                }
            } else {
                log.error("设备ID为空，无法发送通知: orderId={}", orderPrinter.getOrderId());
            }
        } catch (Exception e) {
            log.error("发送设备通知异常: deviceId={}, orderId={}, error={}",
                    orderPrinter.getDeviceId(), orderPrinter.getOrderId(), e.getMessage(), e);
        }
    }

    /**
     * 处理打印机订单支付回调（供HLPay调用）
     * @param orderPrinter 打印机订单
     * @param channelType 支付渠道类型
     * @param transactionId 交易流水号
     * @param payTime 支付时间
     * @param orderPrinterService 订单服务
     * @param orderCollectService 收入统计服务
     * @param devicePrinterService 设备服务
     * @return 处理结果
     */
    public static String handlePrinterOrderCallback(
            OrderPrinter orderPrinter,
            String channelType,
            String transactionId,
            Date payTime,
            IOrderPrinterService orderPrinterService,
            IOrderCollectService orderCollectService,
            IDevicePrinterService devicePrinterService) {

        try {
            String orderId = orderPrinter.getOrderId();
            log.info("开始处理打印机订单支付回调: orderId={}", orderId);

            // 验证订单状态，避免重复处理
            if (orderPrinter.getOrderStatus() != 0) {
                log.warn("打印机订单状态异常，可能重复回调: orderId={}, currentStatus={}", orderId, orderPrinter.getOrderStatus());
                return "SUCCESS";
            }

            // 更新订单支付信息
            orderPrinter.setPayTime(payTime);
            orderPrinter.setTransactionId(transactionId);
            orderPrinter.setOrderStatus(1); // 已支付，等待打印
            orderPrinter.setPayWay(channelType.equals("WX") ? 1 : 2); // 1-微信 2-支付宝
            orderPrinterService.updateOrderPrinter(orderPrinter);

            // 更新所有关联的打印任务状态
            updatePrintTasksStatusStatic(orderId, orderPrinterService);

            // 通知设备开始打印
            notifyDeviceToPrintStatic(orderPrinter);

            // 处理分账订单（仅在启用分账时）
            List<OrderPrinter> splitOrders = new ArrayList<>();
            if (ENABLE_SPLIT_ACCOUNT) {
                OrderPrinter searchOrder = new OrderPrinter();
                searchOrder.setOrderId("_" + orderId);
                splitOrders = orderPrinterService.selectOrderPrinterList(searchOrder);
                if (splitOrders.size() > 0) {
                    for (OrderPrinter splitOrder : splitOrders) {
                        splitOrder.setOrderStatus(1);
                        splitOrder.setTransactionId(transactionId);
                        splitOrder.setPayTime(payTime);
                        orderPrinterService.updateOrderPrinter(splitOrder);
                    }
                    log.info("分账订单状态更新完成: 主订单={}, 分账订单数量={}", orderId, splitOrders.size());
                }
            } else {
                log.info("简化支付模式，跳过分账订单处理: orderId={}", orderId);
            }

            // 更新收入统计
            updateOrderCollectStatic(orderPrinter, splitOrders, orderCollectService);

            // 更新设备总收入
            updateDeviceTotalIncomeStatic(orderPrinter, devicePrinterService);

            log.info("打印机订单支付回调处理完成: orderId={}, deviceId={}, totalAmount={}",
                    orderId, orderPrinter.getDeviceId(), orderPrinter.getTotalAmount());

            return "SUCCESS";

        } catch (Exception e) {
            log.error("处理打印机订单支付回调失败: orderId={}, error={}", orderPrinter.getOrderId(), e.getMessage(), e);
            throw new RuntimeException("处理打印机订单支付回调失败: " + e.getMessage());
        }
    }

    /**
     * 静态方法：更新打印任务状态
     */
    private static void updatePrintTasksStatusStatic(String orderId, IOrderPrinterService orderPrinterService) {
        try {
            List<OrderPrinterTask> tasks = orderPrinterService.getOrderTasks(orderId);
            if (tasks != null && !tasks.isEmpty()) {
                for (OrderPrinterTask task : tasks) {
                    if (task.getPrintStatus() == 0) { // 只更新待打印的任务
                        task.setPrintStatus(0); // 0-待打印（确保状态正确）
                        orderPrinterService.updateTask(task);
                    }
                }
                log.info("更新打印任务状态成功: orderId={}, taskCount={}", orderId, tasks.size());
            }
        } catch (Exception e) {
            log.error("更新打印任务状态失败: orderId={}, error={}", orderId, e.getMessage(), e);
        }
    }

    /**
     * 静态方法：通知设备开始打印
     */
    private static void notifyDeviceToPrintStatic(OrderPrinter orderPrinter) {
        try {
            String deviceId = orderPrinter.getDeviceId();
            if (StringUtils.isNotEmpty(deviceId)) {
                // 发送支付成功消息，设备收到后开始打印
                int result = WebSocketServer.sendInfo("zfcg", deviceId);
                if (result > 0) {
                    log.info("设备通知发送成功: deviceId={}, orderId={}", deviceId, orderPrinter.getOrderId());
                } else {
                    log.warn("设备通知发送失败，设备可能离线: deviceId={}, orderId={}", deviceId, orderPrinter.getOrderId());
                }
            } else {
                log.error("设备ID为空，无法发送通知: orderId={}", orderPrinter.getOrderId());
            }
        } catch (Exception e) {
            log.error("发送设备通知异常: deviceId={}, orderId={}, error={}",
                    orderPrinter.getDeviceId(), orderPrinter.getOrderId(), e.getMessage(), e);
        }
    }

    /**
     * 静态方法：更新收入统计
     */
    private static void updateOrderCollectStatic(OrderPrinter orderPrinter, List<OrderPrinter> splitOrders, IOrderCollectService orderCollectService) {
        try {
            // 汇总（主订单）
            OrderCollect collect = orderCollectService.query()
                .eq("merchant_id", orderPrinter.getMchid())
                .eq("time", DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderPrinter.getCreateTime()))
                .one();

            if (collect == null) {
                collect = new OrderCollect();
                collect.setMerchantId(orderPrinter.getMchid());
                collect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderPrinter.getCreateTime()));
            }

            // 打印机订单统计到其他收入
            setCollectForPrinterStatic(orderPrinter, collect, orderCollectService);

            // 如果存在分账订单
            if (splitOrders.size() > 0) {
                // 汇总分账人
                for (OrderPrinter splitOrder : splitOrders) {
                    OrderCollect splitCollect = orderCollectService.query()
                        .eq("merchant_id", splitOrder.getMchid())
                        .eq("time", DateUtils.parseDateToStr(DateUtils.YYYY_MM, splitOrder.getCreateTime()))
                        .one();

                    if (splitCollect == null) {
                        splitCollect = new OrderCollect();
                        splitCollect.setMerchantId(splitOrder.getMchid());
                        splitCollect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, splitOrder.getCreateTime()));
                    }
                    setCollectForPrinterStatic(splitOrder, splitCollect, orderCollectService);
                }
            }
        } catch (Exception e) {
            log.error("更新收入统计失败: orderId={}, error={}", orderPrinter.getOrderId(), e.getMessage(), e);
        }
    }

    /**
     * 静态方法：设置打印机订单收入统计
     */
    private static void setCollectForPrinterStatic(OrderPrinter order, OrderCollect collect, IOrderCollectService orderCollectService) {
        // 打印机订单统计到其他收入
        Long account = order.getAccount() != null ? order.getAccount() : 0L;
        collect.setOtherIncome(collect.getOtherIncome() + account);
        collect.setOtherOrderCount(collect.getOtherOrderCount() + 1);
        collect.setCount(collect.getCount() + account);

        if (collect.getId() != null) {
            orderCollectService.updateById(collect);
        } else {
            orderCollectService.save(collect);
        }
    }

    /**
     * 静态方法：更新设备总收入
     */
    private static void updateDeviceTotalIncomeStatic(OrderPrinter orderPrinter, IDevicePrinterService devicePrinterService) {
        try {
            DevicePrinter devicePrinter = devicePrinterService.selectDevicePrinterByDeviceId(orderPrinter.getDeviceId());
            if (devicePrinter != null) {
                Long currentPrice = devicePrinter.getCountPrice() != null ? devicePrinter.getCountPrice() : 0L;
                Long orderAmount = orderPrinter.getTotalAmount() != null ? orderPrinter.getTotalAmount() : 0L;
                devicePrinter.setCountPrice(currentPrice + orderAmount);
                devicePrinterService.updateDevicePrinter(devicePrinter);
                log.info("设备总收入更新成功: deviceId={}, 新增金额={}, 总收入={}",
                        orderPrinter.getDeviceId(), orderAmount, currentPrice + orderAmount);
            }
        } catch (Exception e) {
            log.error("更新设备总收入失败: deviceId={}, error={}", orderPrinter.getDeviceId(), e.getMessage(), e);
        }
    }

    /**
     * 从请求中获取当前用户的openid
     * 优先级：1. 从ThreadLocal中获取 2. 从JWT token中获取 3. 返回null
     */
    private String getCurrentUserOpenid(HttpServletRequest request) {
        try {
            // 方法1：尝试从ThreadLocal中获取（如果经过了认证拦截器）
            WxappLoginUser currentUser = UserContext.getCurrentUser();
            if (currentUser != null && StringUtils.isNotEmpty(currentUser.getOpenid())) {
                log.info("从ThreadLocal获取到openid: {}", currentUser.getOpenid());
                return currentUser.getOpenid();
            }

            // 方法2：尝试从JWT token中获取
            WxappLoginUser loginUser = wxAppAuthService.getClientUser(request);
            if (loginUser != null && StringUtils.isNotEmpty(loginUser.getOpenid())) {
                log.info("从JWT token获取到openid: {}", loginUser.getOpenid());
                return loginUser.getOpenid();
            }
        } catch (Exception e) {
            log.warn("获取用户openid失败: {}", e.getMessage());
        }
        return null;
    }
}
