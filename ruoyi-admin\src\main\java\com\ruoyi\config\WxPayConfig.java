package com.ruoyi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信支付配置类
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wxpay")
public class WxPayConfig {

    /**
     * 微信支付AppID
     */
    private String appId;

    /**
     * 微信支付商户号
     */
    private String mchId = "1631162326";

    /**
     * 微信支付API密钥（API v3密钥）
     */
    private String apiKey;

    /**
     * 微信支付证书序列号
     */
    private String certSerialNo;

    /**
     * 微信支付私钥文件路径
     */
    private String privateKeyPath;

    /**
     * 微信支付证书文件路径
     */
    private String certPath;

    /**
     * 微信支付回调地址
     */
    private String notifyUrl;

    /**
     * 微信支付API基础URL
     */
    private String apiBaseUrl = "https://api.mch.weixin.qq.com";

    /**
     * 是否为沙箱环境
     */
    private boolean sandbox = false;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 8000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 10000;

    /**
     * 获取JSAPI支付URL
     */
    public String getJsapiUrl() {
        return apiBaseUrl + "/v3/pay/transactions/jsapi";
    }

    /**
     * 获取Native支付URL
     */
    public String getNativeUrl() {
        return apiBaseUrl + "/v3/pay/transactions/native";
    }

    /**
     * 获取订单查询URL
     */
    public String getQueryUrl(String outTradeNo) {
        return apiBaseUrl + "/v3/pay/transactions/out-trade-no/" + outTradeNo + "?mchid=" + mchId;
    }

    /**
     * 获取退款URL
     */
    public String getRefundUrl() {
        return apiBaseUrl + "/v3/refund/domestic/refunds";
    }

    /**
     * 获取退款查询URL
     */
    public String getRefundQueryUrl(String outRefundNo) {
        return apiBaseUrl + "/v3/refund/domestic/refunds/" + outRefundNo;
    }
}
